package com.example.medicine.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;
import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "supplier")
public class Supplier {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String name;
    private String contact;
    private String phone;
    private String email;
    private String address;
    private String remark;

    // 数据库中存储语义化字符串状态
    private String status;

    @Column(name = "create_time")
    private Date createTime;

    // 状态常量定义
    public static final String STATUS_ACTIVE = "ACTIVE";   // 正常
    public static final String STATUS_INACTIVE = "INACTIVE"; // 停用

    /**
     * 将数据库中的字符串状态转换为前端需要的数字状态
     * ACTIVE -> 1 (正常)
     * INACTIVE -> 0 (停用)
     */
    @JsonGetter("status")
    public Integer getStatusAsNumber() {
        if (STATUS_ACTIVE.equals(this.status)) {
            return 1;
        } else if (STATUS_INACTIVE.equals(this.status)) {
            return 0;
        }
        // 默认返回1（正常状态）
        return 1;
    }

    /**
     * 将前端传入的数字状态转换为数据库需要的字符串状态
     * 1 -> ACTIVE (正常)
     * 0 -> INACTIVE (停用)
     */
    @JsonSetter("status")
    public void setStatusFromNumber(Integer statusNumber) {
        if (statusNumber != null && statusNumber == 0) {
            this.status = STATUS_INACTIVE;
        } else {
            // 默认设置为正常状态
            this.status = STATUS_ACTIVE;
        }
    }

    /**
     * 获取原始的字符串状态（用于数据库操作）
     */
    public String getRawStatus() {
        return this.status;
    }

    /**
     * 设置原始的字符串状态（用于数据库操作）
     */
    public void setRawStatus(String status) {
        this.status = status;
    }

    @PrePersist
    protected void onCreate() {
        if (createTime == null) {
            createTime = new Date();
        }
        if (status == null) {
            status = STATUS_ACTIVE;
        }
    }
}