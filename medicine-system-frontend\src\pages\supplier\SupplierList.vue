<template>
  <div class="supplier-list-page">
    <div class="page-header">
      <h2>供应商管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加供应商
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="supplierList"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="供应商名称" min-width="150" />
      <el-table-column prop="contact" label="联系人" width="120" />
      <el-table-column prop="address" label="地址" min-width="200" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button 
            :type="row.status === 1 ? 'warning' : 'success'" 
            link 
            @click="handleToggleStatus(row)"
          >
            {{ row.status === 1 ? '停用' : '启用' }}
          </el-button>
          <el-popconfirm
            title="确定要删除这条数据吗？"
            @confirm="handleDelete(row.id)"
          >
            <template #reference>
              <el-button type="danger" link>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="fetchSupplierList"
        @current-change="fetchSupplierList"
      />
    </div>
    
    <!-- 供应商表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <SupplierForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getSupplierList, deleteSupplier, toggleSupplierStatus } from '@/api/modules/supplier';
import type { Supplier, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import SupplierForm from './SupplierForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '供应商名称', prop: 'name', type: 'input' },
    { label: '联系人', prop: 'contact', type: 'input' },
    { label: '地址', prop: 'address', type: 'input' },
    { 
      label: '状态', 
      prop: 'status', 
      type: 'select',
      options: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  ]
});

// 数据列表
const supplierList = ref<Supplier[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加供应商',
    'edit': '编辑供应商',
    'view': '查看供应商详情'
  };
  return titleMap[formMode.value];
});

// 获取供应商列表
const fetchSupplierList = async () => {
  loading.value = true;
  try {
    const result = await getSupplierList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    supplierList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取供应商列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchSupplierList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchSupplierList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchSupplierList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Supplier) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: Supplier) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deleteSupplier(id);
    ElMessage.success('删除成功');
    fetchSupplierList();
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理启用/停用
const handleToggleStatus = async (row: Supplier) => {
  const newStatus = row.status === 1 ? 0 : 1;
  const action = newStatus === 1 ? '启用' : '停用';
  
  try {
    await ElMessageBox.confirm(`确定要${action}这个供应商吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await toggleSupplierStatus(row.id!, newStatus);
    ElMessage.success(`${action}成功`);
    fetchSupplierList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || `${action}失败`);
    }
  }
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchSupplierList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchSupplierList();
});
</script>

<style scoped>
.supplier-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>