-- 插入角色数据
INSERT INTO role (id, role_name, permissions) VALUES 
(1, '超级管理员', 'user:view,user:add,user:edit,user:delete,role:view,role:add,role:edit,role:delete,medicine:view,medicine:add,medicine:edit,medicine:delete,inventory:view,inventory:add,inventory:edit,inventory:delete,purchase:view,purchase:add,purchase:edit,purchase:delete,sale:view,sale:add,sale:edit,sale:delete,supplier:view,supplier:add,supplier:edit,supplier:delete,customer:view,customer:add,customer:edit,customer:delete,report:view,report:export,system:settings,system:logs,system:statistics'),
(2, '管理员', 'user:view,user:add,user:edit,role:view,medicine:view,medicine:add,medicine:edit,inventory:view,inventory:add,inventory:edit,purchase:view,purchase:add,sale:view,sale:add,supplier:view,supplier:add,customer:view,customer:add,report:view'),
(3, '采购员', 'medicine:view,inventory:view,purchase:view,purchase:add,purchase:edit,supplier:view,supplier:add'),
(4, '销售员', 'medicine:view,inventory:view,sale:view,sale:add,sale:edit,customer:view,customer:add'),
(5, '仓库管理员', 'medicine:view,inventory:view,inventory:add,inventory:edit')
ON DUPLICATE KEY UPDATE 
role_name = VALUES(role_name),
permissions = VALUES(permissions);

-- 插入用户数据
INSERT INTO user (id, username, password, role_id, status, create_time, last_login_time, email, phone, real_name, department, position) VALUES 
(1, 'admin', 'admin123', 1, 'ACTIVE', NOW(), NOW(), '<EMAIL>', '13800138000', '系统管理员', '信息技术部', '系统管理员'),
(2, 'manager', 'manager123', 2, 'ACTIVE', NOW(), NOW(), '<EMAIL>', '13800138001', '张经理', '管理部', '部门经理'),
(3, 'buyer', 'buyer123', 3, 'ACTIVE', NOW(), NOW(), '<EMAIL>', '13800138002', '李采购', '采购部', '采购员'),
(4, 'seller', 'seller123', 4, 'ACTIVE', NOW(), NOW(), '<EMAIL>', '13800138003', '王销售', '销售部', '销售员'),
(5, 'warehouse', 'warehouse123', 5, 'ACTIVE', NOW(), NOW(), '<EMAIL>', '13800138004', '赵仓管', '仓储部', '仓库管理员'),
(6, 'test_user', 'test123', 2, 'INACTIVE', NOW(), NULL, '<EMAIL>', '13800138005', '测试用户', '测试部', '测试员')
ON DUPLICATE KEY UPDATE 
username = VALUES(username),
password = VALUES(password),
role_id = VALUES(role_id),
status = VALUES(status),
email = VALUES(email),
phone = VALUES(phone),
real_name = VALUES(real_name),
department = VALUES(department),
position = VALUES(position);
