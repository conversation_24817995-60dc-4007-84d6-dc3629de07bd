<template>
  <div class="customer-list-page">
    <div class="page-header">
      <h2>客户管理</h2>
      <el-button type="primary" @click="handleAdd">
        <el-icon><Plus /></el-icon>添加客户
      </el-button>
    </div>
    
    <!-- 搜索表单 -->
    <SearchForm 
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />
    
    <!-- 数据表格 -->
    <el-table 
      v-loading="loading"
      :data="customerList"
      border
      style="width: 100%"
    >
      <el-table-column prop="name" label="客户名称" min-width="150" />
      <el-table-column prop="contact" label="联系人" width="120" />
      <el-table-column prop="address" label="地址" min-width="200" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === 1 ? 'success' : 'danger'">
            {{ row.status === 1 ? '正常' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="320" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button type="primary" link size="small" @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link size="small" @click="handleView(row)">查看</el-button>
            <el-button type="info" link size="small" @click="handleViewHistory(row)">购买记录</el-button>
            <el-button
              :type="row.status === 1 ? 'warning' : 'success'"
              link
              size="small"
              @click="handleToggleStatus(row)"
            >
              {{ row.status === 1 ? '停用' : '启用' }}
            </el-button>
            <el-popconfirm
              title="确定要删除这条数据吗？"
              @confirm="handleDelete(row.id)"
            >
              <template #reference>
                <el-button type="danger" link size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      />
    </div>
    
    <!-- 客户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <CustomerForm
        :id="selectedId"
        :mode="formMode"
        @success="handleFormSuccess"
        @cancel="dialogVisible = false"
      />
    </el-dialog>
    
    <!-- 购买记录对话框 -->
    <el-dialog
      v-model="historyDialogVisible"
      title="客户购买记录"
      width="70%"
      destroy-on-close
    >
      <el-table :data="purchaseHistory" border>
        <el-table-column prop="medicineName" label="药品名称" />
        <el-table-column prop="quantity" label="数量" width="80" />
        <el-table-column prop="price" label="单价" width="100">
          <template #default="{ row }">
            ¥{{ row.price?.toFixed(2) || '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="totalAmount" label="总金额" width="100">
          <template #default="{ row }">
            ¥{{ ((row.quantity || 0) * (row.price || 0)).toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="saleDate" label="购买日期" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="row.status === 'COMPLETED' ? 'success' : 'warning'">
              {{ row.status === 'COMPLETED' ? '已完成' : '已退货' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getCustomerList, deleteCustomer, toggleCustomerStatus, getCustomerPurchaseHistory } from '@/api/modules/customer';
import type { Customer, Sale, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';
import CustomerForm from './CustomerForm.vue';

// 搜索配置
const searchConfig = reactive({
  fields: [
    { label: '客户名称', prop: 'name', type: 'input' },
    { label: '联系人', prop: 'contact', type: 'input' },
    { label: '地址', prop: 'address', type: 'input' },
    { 
      label: '状态', 
      prop: 'status', 
      type: 'select',
      options: [
        { label: '正常', value: 1 },
        { label: '停用', value: 0 }
      ]
    }
  ]
});

// 数据列表
const customerList = ref<Customer[]>([]);
const loading = ref(false);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(10);
const searchParams = reactive<PageParams>({
  page: 1,
  size: 10,
  keyword: ''
});

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit' | 'view'>('add');
const selectedId = ref<number | undefined>(undefined);
const dialogTitle = computed(() => {
  const titleMap = {
    'add': '添加客户',
    'edit': '编辑客户',
    'view': '查看客户详情'
  };
  return titleMap[formMode.value];
});

// 购买记录对话框相关
const historyDialogVisible = ref(false);
const purchaseHistory = ref<Sale[]>([]);

// 获取客户列表
const fetchCustomerList = async () => {
  loading.value = true;
  try {
    const result = await getCustomerList({
      ...searchParams,
      page: currentPage.value,
      size: pageSize.value
    });
    customerList.value = result.records;
    total.value = result.total;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取客户列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchCustomerList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchCustomerList();
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  fetchCustomerList();
});

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Customer) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理查看
const handleView = (row: Customer) => {
  formMode.value = 'view';
  selectedId.value = row.id;
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (id: number) => {
  try {
    await deleteCustomer(id);
    ElMessage.success('删除成功');
    fetchCustomerList();
  } catch (error: any) {
    ElMessage.error(error.msg || '删除失败');
  }
};

// 处理启用/停用
const handleToggleStatus = async (row: Customer) => {
  const newStatus = row.status === 1 ? 0 : 1;
  const action = newStatus === 1 ? '启用' : '停用';
  
  try {
    await ElMessageBox.confirm(`确定要${action}这个客户吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    
    await toggleCustomerStatus(row.id!, newStatus);
    ElMessage.success(`${action}成功`);
    fetchCustomerList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || `${action}失败`);
    }
  }
};

// 处理查看购买记录
const handleViewHistory = async (row: Customer) => {
  try {
    const result = await getCustomerPurchaseHistory(row.id!, { page: 1, size: 100 });
    purchaseHistory.value = result.records;
    historyDialogVisible.value = true;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取购买记录失败');
  }
};

// 处理表单提交成功
const handleFormSuccess = () => {
  dialogVisible.value = false;
  fetchCustomerList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchCustomerList();
});
</script>

<style scoped>
.customer-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.action-buttons .el-button:hover {
  transform: translateY(-1px);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>