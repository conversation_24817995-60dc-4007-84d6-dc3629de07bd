package com.example.medicine.entity;

import lombok.Data;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonSetter;

import javax.persistence.*;
import java.util.Date;

@Data
@Entity
@Table(name = "user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private String username;
    private String password;
    private Long roleId;

    // 数据库中存储语义化字符串状态
    private String status;

    private Date createTime;
    private Date lastLoginTime;

    // Profile related fields
    private String email;
    private String phone;
    private String avatar;
    private String realName;
    private String department;
    private String position;

    // Settings related fields (stored as JSON)
    @Column(columnDefinition = "TEXT")
    private String userSettings;

    // 状态常量定义
    public static final String STATUS_ACTIVE = "ACTIVE";   // 正常
    public static final String STATUS_INACTIVE = "INACTIVE"; // 停用

    /**
     * 将数据库中的字符串状态转换为前端需要的数字状态
     * ACTIVE -> 1 (正常)
     * INACTIVE -> 0 (停用)
     */
    @JsonGetter("status")
    public Integer getStatusAsNumber() {
        if (STATUS_ACTIVE.equals(this.status)) {
            return 1;
        } else if (STATUS_INACTIVE.equals(this.status)) {
            return 0;
        }
        // 默认返回1（正常状态）
        return 1;
    }

    /**
     * 将前端传入的数字状态转换为数据库需要的字符串状态
     * 1 -> ACTIVE (正常)
     * 0 -> INACTIVE (停用)
     */
    @JsonSetter("status")
    public void setStatusFromNumber(Integer statusNumber) {
        if (statusNumber != null && statusNumber == 0) {
            this.status = STATUS_INACTIVE;
        } else {
            // 默认设置为正常状态
            this.status = STATUS_ACTIVE;
        }
    }

    /**
     * 获取原始的字符串状态（用于数据库操作）
     */
    public String getRawStatus() {
        return this.status;
    }

    /**
     * 设置原始的字符串状态（用于数据库操作）
     */
    public void setRawStatus(String status) {
        this.status = status;
    }

    /**
     * 规范化状态值，确保状态值的一致性
     */
    public void normalizeStatus() {
        if (this.status != null) {
            String upperStatus = this.status.toUpperCase();
            if ("ACTIVE".equals(upperStatus)) {
                this.status = STATUS_ACTIVE;
            } else if ("INACTIVE".equals(upperStatus)) {
                this.status = STATUS_INACTIVE;
            } else {
                // 默认设置为正常状态
                this.status = STATUS_ACTIVE;
            }
        } else {
            this.status = STATUS_ACTIVE;
        }
    }
}