-- 为supplier表添加新字段
ALTER TABLE `supplier` 
ADD COLUMN `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '联系电话' AFTER `contact`,
ADD COLUMN `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱地址' AFTER `phone`,
ADD COLUMN `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注信息' AFTER `address`;

-- 更新现有数据，添加示例联系方式和备注
UPDATE `supplier` SET 
  `phone` = '010-88888888', 
  `email` = '<EMAIL>', 
  `remark` = '国内最大的医药流通企业' 
WHERE `id` = 1;

UPDATE `supplier` SET 
  `phone` = '021-66666666', 
  `email` = '<EMAIL>', 
  `remark` = '华润集团旗下医药企业' 
WHERE `id` = 2;

UPDATE `supplier` SET 
  `phone` = '021-77777777', 
  `email` = '<EMAIL>', 
  `remark` = '上海医药集团控股公司' 
WHERE `id` = 3;

UPDATE `supplier` SET 
  `phone` = '027-88889999', 
  `email` = '<EMAIL>', 
  `remark` = '全国性医药流通企业' 
WHERE `id` = 4;

UPDATE `supplier` SET 
  `phone` = '0871-12345678', 
  `email` = '<EMAIL>', 
  `remark` = '知名中药制药企业' 
WHERE `id` = 5;

UPDATE `supplier` SET 
  `phone` = '0451-87654321', 
  `email` = '<EMAIL>', 
  `remark` = '东北地区大型制药企业' 
WHERE `id` = 6;
