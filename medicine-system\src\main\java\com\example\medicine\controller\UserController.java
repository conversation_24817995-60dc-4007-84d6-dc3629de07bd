package com.example.medicine.controller;

import com.example.medicine.entity.User;
import com.example.medicine.entity.Role;
import com.example.medicine.dto.UserDTO;
import com.example.medicine.service.UserService;
import com.example.medicine.service.RoleService;
import com.example.medicine.common.Result;
import com.example.medicine.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/user")
@CrossOrigin(origins = "http://localhost:3000")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private RoleService roleService;

    @GetMapping("/list")
    public Result<PageResult<UserDTO>> getUserList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String keyword) {
        try {
            List<User> allUsers = userService.findAll();
            List<Role> allRoles = roleService.findAll();

            // 创建角色映射
            Map<Long, String> roleMap = allRoles.stream()
                .collect(Collectors.toMap(Role::getId, Role::getRoleName));

            // 转换为 UserDTO 并添加角色名称
            List<UserDTO> userDTOs = allUsers.stream()
                .map(user -> {
                    UserDTO dto = new UserDTO();
                    dto.setId(user.getId());
                    dto.setUsername(user.getUsername());
                    dto.setRoleId(user.getRoleId());
                    dto.setRoleName(roleMap.getOrDefault(user.getRoleId(), "未知角色"));
                    dto.setStatus(user.getStatus());
                    dto.setCreateTime(user.getCreateTime());
                    dto.setLastLoginTime(user.getLastLoginTime());
                    dto.setEmail(user.getEmail());
                    dto.setPhone(user.getPhone());
                    dto.setAvatar(user.getAvatar());
                    dto.setRealName(user.getRealName());
                    dto.setDepartment(user.getDepartment());
                    dto.setPosition(user.getPosition());
                    return dto;
                })
                .collect(Collectors.toList());

            // 如果有关键字搜索，进行过滤
            if (!keyword.isEmpty()) {
                userDTOs = userDTOs.stream()
                    .filter(user ->
                        user.getUsername().toLowerCase().contains(keyword.toLowerCase()) ||
                        (user.getRoleName() != null && user.getRoleName().toLowerCase().contains(keyword.toLowerCase()))
                    )
                    .collect(Collectors.toList());
            }

            // 简单的分页逻辑
            int total = userDTOs.size();
            int start = (page - 1) * size;
            int end = Math.min(start + size, total);

            List<UserDTO> records = start < total ? userDTOs.subList(start, end) : List.of();

            PageResult<UserDTO> pageResult = new PageResult<>();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setCurrent(page);
            pageResult.setSize(size);

            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Long id) {
        try {
            return Result.success(userService.findById(id));
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/add")
    public Result<User> addUser(@RequestBody User user) {
        try {
            // 规范化状态值
            user.normalizeStatus();
            return Result.success(userService.save(user));
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    @PutMapping("/update/{id}")
    public Result<User> updateUser(@PathVariable Long id, @RequestBody User user) {
        try {
            user.setId(id);
            // 规范化状态值
            user.normalizeStatus();
            return Result.success(userService.save(user));
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteById(id);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @PostMapping("/toggle-status/{id}")
    public Result<Void> toggleUserStatus(@PathVariable Long id, @RequestBody java.util.Map<String, String> request) {
        try {
            String status = request.get("status");
            User user = userService.findById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 验证状态值
            if (!User.STATUS_ACTIVE.equals(status) && !User.STATUS_INACTIVE.equals(status)) {
                return Result.error("无效的状态值");
            }

            // 设置新状态
            user.setRawStatus(status);
            // 规范化状态值，确保一致性
            user.normalizeStatus();
            userService.save(user);

            return Result.success(null);
        } catch (Exception e) {
            return Result.error("状态更新失败: " + e.getMessage());
        }
    }

    @PostMapping("/reset-password/{id}")
    public Result<Void> resetUserPassword(@PathVariable Long id, @RequestBody java.util.Map<String, String> request) {
        try {
            String newPassword = request.get("newPassword");
            User user = userService.findById(id);
            if (user == null) {
                return Result.error("用户不存在");
            }

            // 设置新密码（实际项目中应该加密）
            user.setPassword(newPassword);
            userService.save(user);

            return Result.success(null);
        } catch (Exception e) {
            return Result.error("密码重置失败: " + e.getMessage());
        }
    }
}