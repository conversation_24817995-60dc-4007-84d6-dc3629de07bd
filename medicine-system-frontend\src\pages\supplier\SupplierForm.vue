<template>
  <div class="supplier-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      :disabled="mode === 'view'"
      class="form-content"
    >
      <div class="form-row">
        <el-form-item label="* 供应商名称" prop="name" class="form-item">
          <el-input
            v-model="formData.name"
            placeholder="请输入供应商名称"
            clearable
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="* 联系人" prop="contact" class="form-item">
          <el-input
            v-model="formData.contact"
            placeholder="请输入联系人姓名"
            clearable
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
      </div>

      <div class="form-row">
        <el-form-item label="* 联系电话" prop="phone" class="form-item">
          <el-input
            v-model="formData.phone"
            placeholder="请输入联系电话"
            clearable
            maxlength="20"
          />
        </el-form-item>

        <el-form-item label="邮箱" prop="email" class="form-item">
          <el-input
            v-model="formData.email"
            placeholder="请输入邮箱地址"
            clearable
            maxlength="100"
          />
        </el-form-item>
      </div>

      <el-form-item label="* 地址" prop="address">
        <el-input
          v-model="formData.address"
          type="textarea"
          :rows="3"
          placeholder="请输入详细地址"
          maxlength="255"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="1">正常</el-radio>
          <el-radio :label="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item class="form-actions">
        <el-button
          v-if="mode !== 'view'"
          type="primary"
          @click="handleSubmit"
          :loading="loading"
          size="large"
        >
          {{ mode === 'edit' ? '更新供应商' : '创建供应商' }}
        </el-button>
        <el-button
          @click="handleCancel"
          size="large"
        >
          {{ mode === 'view' ? '返回' : '取消' }}
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { getSupplierDetail, addSupplier, updateSupplier } from '@/api/modules/supplier';
import type { Supplier } from '@/types';

// 定义组件的props
const props = defineProps<{
  id?: number;
  mode?: 'add' | 'edit' | 'view';
}>();

// 定义组件的事件
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'cancel'): void;
}>();

// 表单引用和加载状态
const formRef = ref<FormInstance>();
const loading = ref(false);

// 表单数据
const formData = reactive<Supplier>({
  name: '',
  contact: '',
  phone: '',
  email: '',
  address: '',
  remark: '',
  status: 1
});

// 表单验证规则
const rules = reactive<FormRules>({
  name: [
    { required: true, message: '请输入供应商名称', trigger: 'blur' },
    { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  contact: [
    { required: true, message: '请输入联系人', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/, message: '请输入正确的电话号码', trigger: 'blur' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  address: [
    { required: true, message: '请输入地址', trigger: 'blur' },
    { min: 5, max: 255, message: '长度在 5 到 255 个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 500, message: '备注不能超过 500 个字符', trigger: 'blur' }
  ]
});

// 获取供应商详情
const fetchSupplierDetail = async (id: number) => {
  try {
    loading.value = true;
    const response = await getSupplierDetail(id);
    const detail = response.data || response;

    // 安全地更新表单数据
    Object.keys(detail).forEach(key => {
      if (key in formData) {
        formData[key as keyof Supplier] = detail[key as keyof Supplier];
      }
    });
  } catch (error: any) {
    ElMessage.error(error.msg || '获取供应商详情失败');
  } finally {
    loading.value = false;
  }
};

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true;

        // 准备提交数据
        const submitData = {
          ...formData,
          // 确保状态字段正确
          status: Number(formData.status)
        };

        if (props.mode === 'edit' && props.id) {
          await updateSupplier(props.id, submitData);
          ElMessage.success('供应商更新成功！');
        } else {
          await addSupplier(submitData);
          ElMessage.success('供应商添加成功！');
        }
        emit('success');
      } catch (error: any) {
        ElMessage.error(error.msg || '操作失败，请重试');
      } finally {
        loading.value = false;
      }
    }
  });
};

// 处理取消
const handleCancel = () => {
  emit('cancel');
};

// 页面加载时获取数据
onMounted(async () => {
  if ((props.mode === 'edit' || props.mode === 'view') && props.id) {
    await fetchSupplierDetail(props.id);
  }
});
</script>

<style scoped>
.supplier-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-content {
  padding: 0;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 0;
}

.form-item {
  margin-bottom: 24px;
}

.form-content :deep(.el-form-item) {
  margin-bottom: 24px;
}

.form-content :deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--text-color-primary);
  line-height: 1.5;
}

.form-content :deep(.el-input__inner) {
  border-radius: var(--border-radius);
  transition: var(--transition-all);
}

.form-content :deep(.el-input__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.form-content :deep(.el-textarea__inner) {
  border-radius: var(--border-radius);
  transition: var(--transition-all);
  resize: vertical;
}

.form-content :deep(.el-textarea__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.form-content :deep(.el-radio-group) {
  display: flex;
  gap: 24px;
}

.form-content :deep(.el-radio) {
  margin-right: 0;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

.form-actions :deep(.el-button) {
  min-width: 120px;
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .form-content :deep(.el-form-item__label) {
    width: 100px !important;
  }

  .form-actions :deep(.el-button) {
    width: 100%;
    margin: 8px 0;
  }
}

/* 暗黑模式适配 */
body.dark .form-content :deep(.el-input__inner),
body.dark .form-content :deep(.el-textarea__inner) {
  background-color: var(--bg-color-tertiary);
  border-color: var(--border-color);
  color: var(--text-color-primary);
}

body.dark .form-content :deep(.el-input__inner:focus),
body.dark .form-content :deep(.el-textarea__inner:focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}
</style>