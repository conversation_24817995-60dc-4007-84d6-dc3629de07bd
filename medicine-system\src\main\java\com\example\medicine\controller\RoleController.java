package com.example.medicine.controller;

import com.example.medicine.entity.Role;
import com.example.medicine.service.RoleService;
import com.example.medicine.common.Result;
import com.example.medicine.common.PageResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/role")
@CrossOrigin(origins = "http://localhost:3000")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @GetMapping("/list")
    public Result<PageResult<Role>> getRoleList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "") String keyword) {
        try {
            List<Role> allRoles = roleService.findAll();

            // 如果有关键字搜索，进行过滤
            if (!keyword.isEmpty()) {
                allRoles = allRoles.stream()
                    .filter(role ->
                        role.getRoleName().toLowerCase().contains(keyword.toLowerCase())
                    )
                    .collect(java.util.stream.Collectors.toList());
            }

            // 简单的分页逻辑
            int total = allRoles.size();
            int start = (page - 1) * size;
            int end = Math.min(start + size, total);

            List<Role> records = start < total ? allRoles.subList(start, end) : List.of();

            PageResult<Role> pageResult = new PageResult<>();
            pageResult.setRecords(records);
            pageResult.setTotal(total);
            pageResult.setCurrent(page);
            pageResult.setSize(size);

            return Result.success(pageResult);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/all")
    public Result<List<Role>> getAllRoles() {
        try {
            return Result.success(roleService.findAll());
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    public Result<Role> getRoleById(@PathVariable Long id) {
        try {
            return Result.success(roleService.findById(id));
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @PostMapping("/add")
    public Result<Role> addRole(@RequestBody Role role) {
        try {
            return Result.success(roleService.save(role));
        } catch (Exception e) {
            return Result.error("添加失败: " + e.getMessage());
        }
    }

    @PutMapping("/update/{id}")
    public Result<Role> updateRole(@PathVariable Long id, @RequestBody Role role) {
        try {
            role.setId(id);
            return Result.success(roleService.save(role));
        } catch (Exception e) {
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/delete/{id}")
    public Result<Void> deleteRole(@PathVariable Long id) {
        try {
            roleService.deleteById(id);
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("删除失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/batch-delete")
    public Result<Void> batchDeleteRole(@RequestBody java.util.Map<String, List<Long>> request) {
        try {
            List<Long> ids = request.get("ids");
            for (Long id : ids) {
                roleService.deleteById(id);
            }
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("批量删除失败: " + e.getMessage());
        }
    }

    @PostMapping("/assign-permissions/{roleId}")
    public Result<Void> assignPermissions(@PathVariable Long roleId, @RequestBody java.util.Map<String, List<String>> request) {
        try {
            List<String> permissions = request.get("permissions");
            Role role = roleService.findById(roleId);
            if (role == null) {
                return Result.error("角色不存在");
            }
            
            // 将权限列表转换为字符串存储
            role.setPermissions(String.join(",", permissions));
            roleService.save(role);
            
            return Result.success(null);
        } catch (Exception e) {
            return Result.error("权限分配失败: " + e.getMessage());
        }
    }

    @GetMapping("/permissions/{roleId}")
    public Result<List<String>> getRolePermissions(@PathVariable Long roleId) {
        try {
            Role role = roleService.findById(roleId);
            if (role == null) {
                return Result.error("角色不存在");
            }
            
            List<String> permissions = List.of();
            if (role.getPermissions() != null && !role.getPermissions().isEmpty()) {
                permissions = List.of(role.getPermissions().split(","));
            }
            
            return Result.success(permissions);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/all-permissions")
    public Result<List<String>> getAllPermissions() {
        try {
            // 返回系统中所有可用的权限
            List<String> allPermissions = List.of(
                "user:view", "user:add", "user:edit", "user:delete",
                "role:view", "role:add", "role:edit", "role:delete",
                "medicine:view", "medicine:add", "medicine:edit", "medicine:delete",
                "inventory:view", "inventory:add", "inventory:edit", "inventory:delete",
                "purchase:view", "purchase:add", "purchase:edit", "purchase:delete",
                "sale:view", "sale:add", "sale:edit", "sale:delete",
                "supplier:view", "supplier:add", "supplier:edit", "supplier:delete",
                "customer:view", "customer:add", "customer:edit", "customer:delete",
                "report:view", "report:export",
                "system:settings", "system:logs", "system:statistics"
            );
            return Result.success(allPermissions);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/statistics")
    public Result<java.util.Map<String, Object>> getRoleStatistics() {
        try {
            List<Role> allRoles = roleService.findAll();
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalRoles", allRoles.size());
            statistics.put("activeRoles", allRoles.size()); // 假设所有角色都是活跃的
            
            return Result.success(statistics);
        } catch (Exception e) {
            return Result.error("查询失败: " + e.getMessage());
        }
    }
}
