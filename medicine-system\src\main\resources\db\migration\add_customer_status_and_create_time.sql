-- 为customer表添加status和create_time字段
-- 执行时间：2025-08-03

-- 添加status字段，默认值为ACTIVE（正常状态）
ALTER TABLE `customer` 
ADD COLUMN `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'ACTIVE' COMMENT '客户状态：ACTIVE-正常，INACTIVE-停用';

-- 添加create_time字段，默认值为当前时间
ALTER TABLE `customer` 
ADD COLUMN `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 为现有数据设置默认状态和创建时间
UPDATE `customer` 
SET `status` = 'ACTIVE', `create_time` = NOW() 
WHERE `status` IS NULL OR `create_time` IS NULL;

-- 添加索引以提高查询性能
CREATE INDEX `idx_customer_status` ON `customer` (`status`);
CREATE INDEX `idx_customer_create_time` ON `customer` (`create_time`);
