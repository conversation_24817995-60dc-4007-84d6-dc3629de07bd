-- 修复用户状态数据一致性问题
-- 将所有小写的状态值转换为标准的大写格式

-- 修复 active -> ACTIVE
UPDATE user SET status = 'ACTIVE' WHERE LOWER(status) = 'active';

-- 修复 inactive -> INACTIVE  
UPDATE user SET status = 'INACTIVE' WHERE LOWER(status) = 'inactive';

-- 修复其他可能的变体
UPDATE user SET status = 'ACTIVE' WHERE status IN ('Active', 'ACTIVE', '1', 'enabled', 'enable', 'normal', 'Normal');
UPDATE user SET status = 'INACTIVE' WHERE status IN ('Inactive', 'INACTIVE', '0', 'disabled', 'disable', 'banned', 'Banned');

-- 对于空值或无效值，设置为默认的 ACTIVE 状态
UPDATE user SET status = 'ACTIVE' WHERE status IS NULL OR status = '' OR status NOT IN ('ACTIVE', 'INACTIVE');

-- 验证修复结果
SELECT 
    status,
    COUNT(*) as count
FROM user 
GROUP BY status
ORDER BY status;
