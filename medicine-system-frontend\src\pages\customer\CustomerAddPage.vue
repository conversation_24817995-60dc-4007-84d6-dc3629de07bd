<template>
  <div class="customer-add-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              <router-link to="/customer">客户管理</router-link>
            </el-breadcrumb-item>
            <el-breadcrumb-item>添加客户</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <h1 class="page-title">
          <el-icon class="title-icon"><Plus /></el-icon>
          添加客户
        </h1>
        <p class="page-description">填写以下信息来创建新的客户记录</p>
      </div>
    </div>

    <!-- 表单容器 -->
    <div class="form-container">
      <div class="form-card">
        <div class="card-header">
          <h3>客户信息</h3>
          <div class="card-actions">
            <el-button @click="handleCancel">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
          </div>
        </div>

        <div class="card-content">
          <CustomerForm
            mode="add"
            @success="handleSuccess"
            @cancel="handleCancel"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus, ArrowLeft } from '@element-plus/icons-vue';
import CustomerForm from './CustomerForm.vue';

// 路由实例
const router = useRouter();

// 处理表单提交成功
const handleSuccess = () => {
  ElMessage.success('客户添加成功！');
  // 跳转回客户列表页面
  router.push('/customer');
};

// 处理取消操作
const handleCancel = () => {
  // 跳转回客户列表页面
  router.push('/customer');
};
</script>

<style scoped>
.customer-add-page {
  min-height: 100vh;
  background: var(--bg-color);
  padding: 0;
}

.page-header {
  background: var(--bg-color-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: 24px 32px;
  margin-bottom: 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.breadcrumb-section {
  margin-bottom: 16px;
}

.breadcrumb-section :deep(.el-breadcrumb__item) {
  font-size: 14px;
}

.breadcrumb-section :deep(.el-breadcrumb__item a) {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition-all);
}

.breadcrumb-section :deep(.el-breadcrumb__item a:hover) {
  color: var(--primary-hover);
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.title-icon {
  font-size: 24px;
  color: var(--primary-color);
}

.page-description {
  font-size: 16px;
  color: var(--text-color-secondary);
  margin: 0;
  line-height: 1.5;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 32px 32px;
}

.form-card {
  background: var(--bg-color-secondary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: var(--transition-all);
}

.form-card:hover {
  box-shadow: var(--box-shadow-hover);
}

.card-header {
  padding: 24px 32px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.card-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-color-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-actions {
  display: flex;
  gap: 12px;
}

.card-content {
  padding: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 16px 20px;
    margin-bottom: 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .form-container {
    padding: 0 20px 20px;
  }

  .card-header {
    padding: 20px;
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .card-content {
    padding: 20px;
  }

  .card-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 暗黑模式适配 */
body.dark .card-header {
  background: linear-gradient(135deg, var(--bg-color-tertiary) 0%, var(--bg-color-secondary) 100%);
}
</style>
